import { DataSource } from "typeorm";
import { GatewayImpls } from "../../framework/core.js";
import { deleteEntity, saveEntity } from "../../framework/gateway_typeorm.js";
import { DataRecording, DataRecordingPlaylist } from "../../plugin/recording/recording_typeorm.js";
import { implFindAllApprovalGroup, implFindAllApprovalGroupNative } from "./impl_approval_group.js";
import { implFindAllApprovalTemplateGroup } from "./impl_approval_template_group.js";
import { implFindAllDepartment } from "./impl_department.js";
import { implFindAllDocumentHistory } from "./impl_document_history.js";
import { implDateNow, implRandomString } from "./impl_internal.js";
import { implFindAllPosition } from "./impl_position.js";
import { ImplFindOnePQTemplate, implFindAllPQTemplate, implFindAllPQVendor, implFindOnePQVendor, implFindVendorPQTemplate } from "./impl_prequalification.js";
import {
  implFindAllProcPlanDetail,
  implFindAllProcPlanHeader,
  implFindProcPlanDetailByFromExcludeDepartment,
  implFindProcPlanDetailByRbtb,
} from "./impl_procplan.js";
import { implFindAllRequisition, implFindRequisitionByFromExcludeDepartment, implFindRequisitionByRbtb, implFindUserAssigned } from "./impl_requisition.js";
import { implFindAllSection } from "./impl_section.js";
import { implFindTypeWork } from "./impl_typework.js";
import { implFindAllUser, implFindAllUserNative, implFindUserSupervisors, implFindUserSupervisorsRoleDepartment } from "./impl_user.js";
import { implFindAllVendor, implFindOneVendor } from "./impl_vendor.js";
import { implFindAllPrItem } from "./impl_pr_item.js";
import { ApprovalGroup } from "./table_approval.js";
import { ApprovalTemplateGroup } from "./table_approval_template.js";
import { Department } from "./table_department.js";
import { DocumentHistory } from "./table_document_history.js";
import { Position } from "./table_position.js";
import { PrequalificationTemplate, PrequalificationVendor } from "./table_prequalification.js";
import { ProcPlanDetail, ProcPlanHeader } from "./table_proc_plan.js";
import { Requisition } from "./table_requisition.js";
import { Section } from "./table_section.js";
import { User } from "./table_user.js";
import { Vendor } from "./table_vendor.js";
import { CIVDVendor } from "./table_civd_vendor.js";
import { implFindSettingValues } from "./impl_setting_values.js";
import { SettingValues } from "./table_setting_values.js";
import { implFindAllVendorCivd, implFindOneVendorCivd } from "./impl_vendor_civd.js";
import { BusinessFieldLicense } from "./table_business_field_license.js";
import { implFindAllBusinessFieldLicense } from "./impl_business_field_license.js";
import { Calendar } from "./table_calendar.js";
import { Delegation } from "./table_delegation.js";
import { PositionSupervisor } from "./table_position_supervisor.js";
import { implFindAllDelegation } from "./impl_delegation.js";
import { implFindAllCalendar } from "./impl_calendar.js";
import { PrItem } from "./table_pr_item.js";
import { Log } from "./table_log.js";

export const defaultFilterSize = 9999999;

export const gateways = (ds: DataSource): GatewayImpls => {
  //

  return {
    //
    saveApprovalTemplateGroups: { isInsertOrModify: true, gateway: saveEntity(ds, ApprovalTemplateGroup) },
    saveDocumentHistory: { isInsertOrModify: true, gateway: saveEntity(ds, DocumentHistory) },
    saveApprovalGroups: { isInsertOrModify: true, gateway: saveEntity(ds, ApprovalGroup) },
    saveProcPlanHeader: { isInsertOrModify: true, gateway: saveEntity(ds, ProcPlanHeader) },
    saveProcPlanDetail: { isInsertOrModify: true, gateway: saveEntity(ds, ProcPlanDetail) },
    saveRequisition: { isInsertOrModify: true, gateway: saveEntity(ds, Requisition) },
    savePQTemplate: { isInsertOrModify: true, gateway: saveEntity(ds, PrequalificationTemplate) },
    savePQVendor: { isInsertOrModify: true, gateway: saveEntity(ds, PrequalificationVendor) },
    saveSettingValues: { isInsertOrModify: true, gateway: saveEntity(ds, SettingValues) },
    saveEmailUser: { isInsertOrModify: true, gateway: saveEntity(ds, User) },
    saveDelegation: { isInsertOrModify: true, gateway: saveEntity(ds, Delegation) },

    deleteApprovalTemplateGroups: { isInsertOrModify: true, gateway: deleteEntity(ds, ApprovalTemplateGroup) },
    deleteApprovalGroups: { isInsertOrModify: true, gateway: deleteEntity(ds, ApprovalGroup) },
    deleteProcPlanHeader: { isInsertOrModify: true, gateway: deleteEntity(ds, ProcPlanHeader) },
    deleteProcPlanDetail: { isInsertOrModify: true, gateway: deleteEntity(ds, ProcPlanDetail) },
    deleteProcPlanDetailByHeaderId: { isInsertOrModify: true, gateway: deleteEntity(ds, ProcPlanDetail) },
    deleteRequisition: { isInsertOrModify: true, gateway: deleteEntity(ds, Requisition) },

    findApprovalTemplateGroup: { isInsertOrModify: false, gateway: implFindAllApprovalTemplateGroup(ds) },
    findApprovalGroupNative: { isInsertOrModify: false, gateway: implFindAllApprovalGroupNative(ds) },
    findDocumentHistory: { isInsertOrModify: false, gateway: implFindAllDocumentHistory(ds) },
    findApprovalGroup: { isInsertOrModify: false, gateway: implFindAllApprovalGroup(ds) },
    findDepartment: { isInsertOrModify: false, gateway: implFindAllDepartment(ds) },
    findPosition: { isInsertOrModify: false, gateway: implFindAllPosition(ds) },
    findSection: { isInsertOrModify: false, gateway: implFindAllSection(ds) },
    findVendor: { isInsertOrModify: false, gateway: implFindAllVendor(ds) },
    findVendorCIVD: { isInsertOrModify: false, gateway: implFindAllVendorCivd(ds) },
    findOneVendorCIVD: { isInsertOrModify: false, gateway: implFindOneVendorCivd(ds) },
    findOneVendor: { isInsertOrModify: false, gateway: implFindOneVendor(ds) },
    findProcPlanHeader: { isInsertOrModify: false, gateway: implFindAllProcPlanHeader(ds) },
    findProcPlanDetail: { isInsertOrModify: false, gateway: implFindAllProcPlanDetail(ds) },
    findProcPlanDetailByRbtb: { isInsertOrModify: false, gateway: implFindProcPlanDetailByRbtb(ds) },
    findProcPlanDetailByFromExcludeDepartment: { isInsertOrModify: false, gateway: implFindProcPlanDetailByFromExcludeDepartment(ds) },
    findRequisition: { isInsertOrModify: false, gateway: implFindAllRequisition(ds) },
    findRequisitionByRbtb: { isInsertOrModify: false, gateway: implFindRequisitionByRbtb(ds) },
    findRequisitionByFromExcludeDepartment: { isInsertOrModify: false, gateway: implFindRequisitionByFromExcludeDepartment(ds) },
    findUserAssigned: { isInsertOrModify: false, gateway: implFindUserAssigned(ds) },
    findUser: { isInsertOrModify: false, gateway: implFindAllUser(ds) },
    findUserNative: { isInsertOrModify: false, gateway: implFindAllUserNative(ds) },
    findUserSupervisors: { isInsertOrModify: false, gateway: implFindUserSupervisors(ds) },
    findUserSupervisorsRoleDepartment: { isInsertOrModify: false, gateway: implFindUserSupervisorsRoleDepartment(ds) },
    findPQTemplate: { isInsertOrModify: false, gateway: implFindAllPQTemplate(ds) },
    findOnePQTemplate: { isInsertOrModify: false, gateway: ImplFindOnePQTemplate(ds) },
    findPQVendor: { isInsertOrModify: false, gateway: implFindAllPQVendor(ds) },
    findVendorPqTemplate: { isInsertOrModify: false, gateway: implFindVendorPQTemplate(ds) },
    findOnePQVendor: { isInsertOrModify: false, gateway: implFindOnePQVendor(ds) },
    findTypeWork: { isInsertOrModify: false, gateway: implFindTypeWork(ds) },
    findPrItem: { isInsertOrModify: false, gateway: implFindAllPrItem(ds) },
    findAllBusinessFieldLicense: { isInsertOrModify: false, gateway: implFindAllBusinessFieldLicense(ds) },
    // findOneBusinessFieldLicense: { isInsertOrModify: false, gateway: ImplFindOneBusinessFieldLicense(ds) },
    findSettingValues: { isInsertOrModify: false, gateway: implFindSettingValues(ds) },
    findDelegation: { isInsertOrModify: false, gateway: implFindAllDelegation(ds) },
    findCalendar: { isInsertOrModify: false, gateway: implFindAllCalendar(ds) },

    dateNow: { isInsertOrModify: false, gateway: implDateNow() },
    randomString: { isInsertOrModify: false, gateway: implRandomString() },
  };
};

export const DatabaseTables = [
  // Core Model
  Vendor,
  User,
  Section,
  Requisition,
  ProcPlanHeader,
  ProcPlanDetail,
  PrequalificationTemplate,
  PrequalificationVendor,
  Position,
  DocumentHistory,
  Department,
  ApprovalGroup,
  ApprovalTemplateGroup,
  CIVDVendor,
  BusinessFieldLicense,
  SettingValues,
  Calendar,
  Delegation,
  PositionSupervisor,
  PrItem,

  // Recording
  DataRecording,
  DataRecordingPlaylist,
  Log,
];
