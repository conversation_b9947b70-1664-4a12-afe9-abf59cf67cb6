import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval, getLastApprovalGroupDurationDays, getSubDocumentWording, moveApproval, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition, SaveRequisition } from "../model/model_requisition.js";
import { User, UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTableData, sendApprovalMail } from "../utility/mailer_2.js";

class Gateways {
  findRequisition: FindRequisition;
  findApprovalGroup: FindApprovalGroup;
  saveRequisition: SaveRequisition;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const requisitionActionApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //
    const now = await o.dateNow(ctx);

    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;
    if (!rq) {
      throw new Error("requisition not found");
    }

    if (rq.status !== "ON_REVIEW") {
      throw new Error("requisition must in ON_REVIEW state");
    }

    validateApprovalAction(req.userLogin, rq.approvalGroup!);

    const approval = getApproval(rq.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    // check all current approval steps is DONE
    const paralelAND = rq.approvalGroup ? rq.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    // current approval steps is paralel
    if (!paralelAND) {
      const [approvalGroups] = await o.findApprovalGroup(ctx, { id: rq.approvalGroup!.id });

      for (const app of approvalGroups[0].approvals) {
        if (app.users && app.users[0].id === req.userLogin.id) {
          app.date = getDateOnly(now);
          app.signer = req.userLogin;
          app.status = "DONE";
        }
      }

      if (approvalGroups.find((app) => app.approvals.every((x) => x.status === "DONE"))) {
        approvalGroups[0].durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, approvalGroups[0], now, o.findCalendar);
      }

      await o.saveApprovalGroups(ctx, approvalGroups)

    } else {
      const autoApprove = await moveApproval(ctx, paralelAND, rq, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

      // create procplan code
      // if (autoApprove) {
      //   if (rq.isAdditionalProcPlan) {
      //     const [rqs, rqsCount] = await o.findRequisition(ctx, {
      //       commodity: rq.commodity,
      //       departmentId: rq.department?.id,
      //       status: "APPROVED",
      //     });

      //     const ct: string = "A";
      //     const cm: string = rq.commodity === "GOODS" ? "1" : rq.commodity === "SERVICES" ? "2" : "Y";
      //     const sequence: number = rqsCount + 1;

      //     rq.procPlanDetailCode = `A075-${(rq.year + 1).toString().substring(2)}-${ct}-${rq.department?.code}${cm}${sequence.toString().padStart(3, "0")}`;
      //   }
      // }
    }


    await o.saveRequisition(ctx, rq);

    const documentName = approval?.subDocumentType;

    await o.saveDocumentHistory(ctx, {
      documentId: rq.id!,
      documentType: "REQUISITION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `${getSubDocumentWording(documentName!)} Approved`,
      user: req.userLogin,
      id: `${rq.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    // Send approval emails to the next sequence if needed
    if (rq.approvalGroup && rq.approvalGroup.sequence! > 1) {
      // set data for email reminder
      for (const approval of rq.approvalGroup.approvals) {
        // Don't send emails if any approval has already been signed
        if (rq.approvalGroup.approvals.some((sign) => sign.signer !== null)) {
          break;
        }

        const ccUsers = getCcEmailRequisition(rq, req.userLogin);
        let approvalUsers: User[] = approval!.users || (approval!.currentUserInPosition ? [approval!.currentUserInPosition] : []);
        let mailData: MailTableData[] = getEmailDataRequisition(rq, req.userLogin, approval.subDocumentType);
        mailData[0].subDocType = approval.subDocumentType;

        for (const approvalUser of approvalUsers) {
          sendApprovalMail({
            sendToUserMail: approvalUser.email!,
            sendToUserName: approvalUser.name!,
            ccUserMail: ccUsers,
          }, mailData, "REQUISITION");
        }
      }
    }

    return {};
  },
};
