import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, getApproval, hasOEApprovalUserDepartment, moveApproval } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition, SaveRequisition } from "../model/model_requisition.js";
import { User, UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber, validateActionByID } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTableData, sendApprovalMail, sendNotificationMail } from "../utility/mailer_2.js";

class Gateways {
  findRequisition: FindRequisition;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  saveRequisition: SaveRequisition;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const requisitionActionSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // TODO: check user login is allowed to do this action
    const now = await o.dateNow(ctx);

    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;
    if (!rq) {
      throw new Error("requisition not found");
    }

    if (rq.status !== "DRAFT") {
      throw new Error("requisition must in DRAFT state");
    }

    validateActionByID(req.userLogin.id, [
      //
      rq.creator?.id!,
      rq.requestFor?.id!,
      ...(rq.requesterBackToBack?.map((x) => x.id) as string[]),
    ]);

    rq.submitter = req.userLogin;

    const approval = getApproval(rq.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = rq.approvalGroup ? rq.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    const autoApprove = await moveApproval(ctx, paralelAND, rq, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.saveRequisition(ctx, rq);

    await o.saveDocumentHistory(ctx, {
      documentId: rq.id!,
      documentType: "REQUISITION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `Requisition Submitted`,
      user: req.userLogin,
      id: `${rq.id.slice(4)}-${formatDateWithSecond(now)}`,
    });


    {
      // 
      const ccUsers = getCcEmailRequisition(rq, req.userLogin);
      const mailData: MailTableData[] = getEmailDataRequisition(rq, req.userLogin);

      // send notification mail
      if (
        req.userLogin.id !== (
          rq.approvalGroup!.approvals[0].users ? rq.approvalGroup!.approvals[0].users[0].id : rq.approvalGroup!.approvals[0].currentUserInPosition?.id
        ) &&
        rq.approvalGroup && rq.approvalGroup.sequence! === 1
      ) {
        const approvalUser = rq.approvalGroup.approvals[0].currentUserInPosition ?? rq.approvalGroup.approvals[0].users![0];
        
        sendNotificationMail({
          sendToUserMail: approvalUser.email!,
          sendToUserName: approvalUser.name!,
          submittedByUserName: rq.submitter.name,
          ccUserMail: ccUsers,
        }, mailData, "REQUISITION");
      }

      // send approval mail requestFor / approval first sequence
      if (rq.approvalGroup && rq.approvalGroup.sequence! > 1) {
        for (const approval of rq.approvalGroup.approvals) {
          let approvalUsers: User[] = approval!.users || (approval!.currentUserInPosition ? [approval!.currentUserInPosition] : []);

          for (const approvalUser of approvalUsers) {
            sendApprovalMail({
              sendToUserMail: approvalUser.email!,
              sendToUserName: approvalUser.name!,
              ccUserMail: ccUsers,
            }, mailData, "REQUISITION");
          }

        }
      }
    }
    return {};
  },
};
