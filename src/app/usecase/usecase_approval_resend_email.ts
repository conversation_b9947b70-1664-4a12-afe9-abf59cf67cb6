import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval } from "../model/model_approval.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition } from "../model/model_requisition.js";
import { User, UserLogin } from "../model/model_user.js";
import { DocumentTemplate, TypeOf } from "../model/vo.js";
import { isUserAdmin } from "../utility/helper.js";
import { MailTableData, sendApprovalMail } from "../utility/mailer_2.js";

class Gateways {
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
}

export class Request {
  documentId: string;
  documentType: TypeOf<typeof DocumentTemplate>;
  userLogin: UserLogin;
}

export class Response {}

export const approvalResendEmail: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    // Validate document type
    const validDocumentTypes = [
      // "PROC_PLAN_UPP",
      // "PROC_PLAN_APP",
      "REQUISITION",
      // "PQ_REQUIREMENT",
      // "PQ_REGISTRATION",
      // "PQ_CLARIFICATION",
      // "PQ_EVALUATION_1",
      // "PQ_EVALUATION_2",
      // "PQ_EVALUATION_3",
      // "PQ_EVALUATION_FINAL",
      // "PQ_SUBMISSION"
    ];

    if (!validDocumentTypes.includes(req.documentType)) {
      throw new Error("Invalid document type");
    }

    // Find current approval group for the document
    const [approvalGroups] = await o.findApprovalGroup(ctx, {
      documentId: req.documentId,
      documentType: req.documentType,
      status: "PROCESSING"
    });

    if (approvalGroups.length === 0) {
      throw new Error("No active approval found for this document");
    }

    const approvalGroup = approvalGroups[0];
    const approval = approvalGroup.approvals.find(app => app.status === "PROCESSING");

    if (!approval) {
      throw new Error("No current approval found");
    }

    // Handle different document types
    if (req.documentType === "REQUISITION") {
      //
      const [requisitions] = await o.findRequisition(ctx, { id: req.documentId });
      const requisition = requisitions.length > 0 ? requisitions[0] : null;

      if (!requisition) {
        throw new Error("Requisition not found");
      }

      // Get email data and send approval emails
      const ccUsers = getCcEmailRequisition(requisition, req.userLogin);
      let approvalUsers: User[] = approval.users || (approval.currentUserInPosition ? [approval.currentUserInPosition] : []);
      let mailData: MailTableData[] = getEmailDataRequisition(requisition, req.userLogin, approval.subDocumentType);
      mailData[0].subDocType = approval.subDocumentType;

      for (const approvalUser of approvalUsers) {
        sendApprovalMail({
          sendToUserMail: approvalUser.email!,
          sendToUserName: approvalUser.name!,
          ccUserMail: ccUsers,
        }, mailData, "REQUISITION");
      }

      return {
        message: "Approval email resend successful",
        sentTo: approvalUsers.map(user => user.email!),
        cc: ccUsers,
      };

    } else {
      throw new Error(`Email resend not yet implemented for document type: ${req.documentType}`);
    }

    return {};
  },
};
