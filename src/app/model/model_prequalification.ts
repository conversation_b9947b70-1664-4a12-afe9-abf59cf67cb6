import { <PERSON><PERSON>and<PERSON> } from "../../framework/core.js";
import { BaseFindManyFilter, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { HasApprovalGroup } from "./model_approval.js";
import { BusinessFieldLicense } from "./model_business_field_license.js";
import { User } from "./model_user.js";
import { Vendor } from "./model_vendor.js";
import {
  Business,
  BusinessClass,
  Commodity,
  CompanyStatus,
  Currency,
  Files,
  HighestExperienceScore,
  PreQualificationPhase,
  PreQualificationState,
  PrequalificationType,
  SharepointFile,
  TenderMethod,
  TypeOf,
  deskripsiBuktiStatusPDN,
  deskripsiDokumenDomisili,
  dokumenK3LL,
} from "./vo.js";

export interface PrequalificationTemplate {
  id: string;

  tenderCode: string;
  title: string;
  generalScopeOfWorks: string;

  assignedUser: User | null;
  requestedBackToBacks: User[];

  tenderMethod: TypeOf<typeof TenderMethod>;

  contractDateStart: Date | null;

  contractDateEnd: Date | null;

  poDateIssuance: Date | null;

  poDateDelivery: Date | null;

  sourceRequisitionId: string;

  businessClass?: TypeOf<typeof BusinessClass>;
  businessType?: TypeOf<typeof Business>;
  businessLicenses?: PQBusinessLicense[];
  // businessLicense?: string;
  // businessFields?: PQBusinessField[];
  localContentLevel?: number;
  convertionRateUSDToIDR?: number;
  additionalPQRequirements?: SharepointFile[] | null;
  currency: TypeOf<typeof Currency>;
  ownerEstimateValue: number;
  workOfLocation: string[];
  // projectLocation: string[];

  companyStatus?: TypeOf<typeof CompanyStatus>;
  domicile?: string; //  apabila nilai di bawah Rp10 M atau US$1 Juta, otomatis East Java kecuali untuk Tender dengan Risiko Tinggi (High RIsk) atau Work Location DI "Jakarta Office" (dilaur East Java).
  highRiskCategory?: string; // HIGH MEDIUM LOW

  basicCapability: number; // (KD / kapabilitas dasar)
  highestExperienceScore?: TypeOf<typeof HighestExperienceScore>; // 1/3 atau 1/5 (nPT / pengalaman tertinggi)

  financialDueDiligence: boolean;
  wavPassingLevel?: string; // >=50%, >=55%, >=60%, 

  otherInformations?: PQOtherInformations[];

  prequalificationType?: TypeOf<typeof PrequalificationType>;
  invitedVendors: Vendor[];
  assignmentDate: Date | null;
  announcementDate?: Date | null;
  pqMeeting?: boolean;
  pqMeetingDate?: Date | null;
  pqRegistrationDate?: Date | null;
  pqSubmissionDate?: Date | null;

  pqAnnouncementDoc?: SharepointFile | null;
  pqStatementLetters?: SharepointFile | null;
  vhseMSQuisionerForm?: SharepointFile | null;
  financialDueDiligenceForm?: SharepointFile | null;
  generalProvisionsRequirementsForm?: SharepointFile | null;
  localCompanyStatementLetters?: SharepointFile | null;
  basicCapabilityCalculationForm?: SharepointFile | null;

  phasesRequirement?: HasApprovalGroup | null;
  phasesRegistration?: HasApprovalGroup | null;
  phasesEvaluation?: {
    submission1: EvaluationSubmission | null;
    submission2: EvaluationSubmission | null;
    submission3: EvaluationSubmission | null;
  };
  //
  phasesClarification?: EvaluationClarification | null;
  currentPhase: TypeOf<typeof PreQualificationPhase>;

  // only when phase REGISTRATION
  pqMeetingInfo?: PQMeetingInfo | null;

  commodity: TypeOf<typeof Commodity>;
  justification?: String | null;
}

export type PQBusinessLicense = {
  license: string;
  fields: BusinessFieldLicense[];
}

export type EvaluationClarification = {
  response: "RESULT" | "MEETING" | "UNDECIDED";
  dueDate?: Date;
  approval: HasApprovalGroup | null;
};

export type EvaluationSubmission = {
  response: "AD" | "AI" | "PF" | "OK" | "UNDECIDED";
  dueDate?: Date;
  history?: History;
  approval: HasApprovalGroup | null;
  failedProcess?: string;
  failedNotes?: string;
};

export interface PrequalificationVendor {
  id: string;
  prequalificationTemplate: PrequalificationTemplate;
  vendor: Vendor;
  entitas?: "consortium" | "tunggal";
  listPerusahaan?: { namaPerusahaan?: string; statusPerusahaan?: string; statusKeanggotaan: "pemuka" | "anggota" }[];
  status:
  | "REGISTRATION_DRAFT" //----------------
  | "REGISTRATION_ON_REVIEW"
  | "REGISTRATION_SUBMIT"
  | "REGISTRATION_DECLINED"
  | "REQUIREMENT_SUBMIT" //---------------- VENDOR SAVE SUBMISSION
  | "SUBMISSION_DRAFT" //---------------- VENDOR SUBMIT SUBMISSION
  | "SUBMISSION_SUBMIT"
  | "EVALUATION_SUBMIT" //----------------
  | "EVALUATION_APPROVED"
  | "EVALUATION_SENDBACK"
  | "CLARIFICATION_SUBMIT" //----------------
  | "CLARIFICATION_APPROVED"
  | "CLARIFICATION_SENDBACK";
  // suratMinat: ResultRemarks[]; // registration a
  // suratSPDA: ResultRemarks[]; // registration b
  // dokumenDomisili: ResultRemarks[]; // registration c
  // suratIzinUsaha: ResultRemarks[]; // registration d
  // sertifikatTKDN: ResultRemarks[]; // registration e
  // suratPernyataanPQ?: SharepointFile | null;
  // suratKuasaPernyataanPQ?: SharepointFile | null;
  // dokumenPembuktionSPDN?: SharepointFile | null;
  // dokumenPerjanjian?: SharepointFile | null;
  // dokumenK3LL?: SharepointFile | null;
  // dokumenEKF?: SharepointFile | null;
  // dokumenLK?: SharepointFile | null;
  // dokumenBPKK?: SharepointFile[];

  phaseRegistration?: VendorPhasePQRegistration;
  phaseSubmission?: VendorPhasePQSubmission;
  phaseClarification?: VendorPhasePQClarification;
  // resultRegistration?: "PASS" | "FAIL" | "UNDECIDED";
}

export type VendorPhasePQClarification = {
  clarificationDocuments?: PQSubmissionInfo;
  clarificationLetter: {
    file: SharepointFile | null;
    date: Date | null;
    number: string | null;
    subject: string | null;
  };
  meetingInfo?: {
    status: "DRAFT" | "SUBMITTED";
    place: string;
    meetingDate: Date;
    meetingTime: string;
    minutesOfMeeting: string[];
    file?: SharepointFile | null;
  } | null;
};

export type PQMeetingInfo = {
  status: "DRAFT" | "SUBMITTED";
  place: string;
  deadlineForSubmission: Date;
  minutesOfMeeting: string[];
  invitedVendorsPqMeeting: PQMeetingInvitedVendors[];
  file?: SharepointFile | null;
};

export type PQMeetingInvitedVendors = {
  vendor: { id: string; name: string };
  title: string;
  name: string;
};

export type VendorPhasePQSubmission = {
  submission1?: PQSubmissionInfo | null;
  submission2?: PQSubmissionInfo | null;
  submission3?: PQSubmissionInfo | null;
};

export type PQSubmissionInfo = {
  entity: "CONSORTIUM" | "SINGLE";
  companyEntities: CompanyEntity[]; // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1
  suratPernyataanPQ: ResultRemarks;
  suratKuasaPernyataanPQ?: ResultRemarks;
  suratSPDA: ResultRemarks; // initial data from pq registration
  dokumenBuktiStatusPDN: ResultRemarks;
  dokumenDomisili: ResultRemarks; // initial data from pq registration
  suratIzinUsaha: ResultRemarks; // initial data from pq registration
  sertifikatTKDN: ResultRemarks; // initial data from pq registration
  suratPerjanjianKonsorsium?: ResultRemarks;
  dokumenK3LL: ResultRemarks;
  summaryExperiences?: ({ experiences: SummaryExperience[] } & { result?: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string | null; notes?: string }) | null;
  dokumenEvaluasiKemampuanFinansial: ResultRemarks;
  dokumenLaporanKeuangan: ResultRemarks;
  otherDocuments?: (PQOtherDocuments & ResultRemarks)[]; // other documents dari PQ Template "otherInformations".show = true, Dokumen bukti pengalaman kerja khusus 3
  resultSummary: "PASS" | "FAIL" | "UNDECIDED";
  submitDate: Date | null;
  evaluationDate: Date | null;
  meetingAdditionalInformationDate?: Date;
  isPublish: boolean; // kalao false berarti masih draft
};

export type CompanyEntity = {
  companies: Vendor;
  companyStatus: "PERUSAHAAN_DALAM_NEGERI" | "PERUSAHAAN_NASIONAL" | "PERUSAHAAN_ASING";
  membership: "PEMUKA" | "ANGGOTA";
};

export type SummaryExperience = {
  contractNo: string;
  title: string;
  businessFields: string[];
  contractDate: Date;
  BASTDate: Date;
  currency: TypeOf<typeof Currency>;
  contractValue: number;
  ownerName: string;
  ownerAddress: string;
  contactPerson: string;
  email: string;
  contactNumber: string;
  salinanKontrak: SharepointFile;
  dokumenBAST: SharepointFile;
  suratRingkasanPenagihan: SharepointFile;
  dokumenKemampuanDasar: SharepointFile;
};

export type VendorPhasePQRegistration = {
  suratMinat: ResultRemarks;
  suratKuasaMinat?: ResultRemarks | null;
  suratSPDA: ResultRemarks;
  dokumenDomisili?: ResultRemarks | null;
  suratIzinUsaha: ResultRemarks;
  sertifikatTKDN?: ResultRemarks | null;
  resultSummary: "PASS" | "FAIL" | "UNDECIDED";
  submitDate: Date | null;
  evaluationDate: Date | null;
};

export type ResultRemarks = {
  file: SharepointFile;
  result?: "PASS" | "FAIL" | "UNDECIDED";
  remarks?: string | null;
  notes?: string | null;
  description?: TypeOf<typeof deskripsiDokumenDomisili> | TypeOf<typeof deskripsiBuktiStatusPDN> | TypeOf<typeof dokumenK3LL> | null;
};

export type FileResultRemarks = {
  result: "PASS" | "FAIL" | "UNDECIDED";
  remarks: string | null;
  notes?: string | null;
};

export type RegistrationResultPayload = {
  suratMinat: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string }; // registration a
  // suratKuasaMinat?: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string } | null; // registration a
  suratSPDA: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string }; // registration b
  dokumenDomisili: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string }; // registration c
  suratIzinUsaha: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string }; // registration d
  sertifikatTKDN: { result: "PASS" | "FAIL" | "UNDECIDED"; remarks?: string }; // registration e
};

export interface PQOtherInformations {
  text: string;
  show: boolean;
}

export interface PQOtherDocuments {
  text: string;
  show: boolean;
  file: SharepointFile;
}

export interface PQBusinessField {
  label: string;
  value: string;
}

export class FindPQTemplateFilter extends BaseFindManyFilter {
  assignedUserId?: string;
  as?: "pic" | "btb";
  keyword?: string;
  currentPhase?: TypeOf<typeof PreQualificationPhase>;
  prequalificationType?: TypeOf<typeof PrequalificationType>;
  tenderCode?: string;
}

export type SavePQTemplate = SaveEntity<PrequalificationTemplate>;
export type FindPQTemplate = FindManyEntity<PrequalificationTemplate, FindPQTemplateFilter>;
export type FindOnePQTemplate = ActionHandler<{ pqId: string }, PrequalificationTemplate | null>;

export class FindPQVendorFilter extends BaseFindManyFilter {
  pqId?: string;
  vendorId?: string;
  currentPhase?: TypeOf<typeof PreQualificationPhase>;
  title?: string;
}

export type SavePQVendor = SaveEntity<PrequalificationVendor>;
export type FindPQVendor = FindManyEntity<PrequalificationVendor, FindPQVendorFilter>;
export type FindOnePQVendor = ActionHandler<{ pqId?: string; vendorPqId?: string; vendorId?: string }, PrequalificationVendor | null>;

export type FindVendorPQTemplate = ActionHandler<
  {
    // prequalificationType?: TypeOf<typeof PrequalificationType>;
    vendorId: string;
    currentPhase?: TypeOf<typeof PreQualificationPhase>;
    title?: string;
    listType?: "myPq" | "pqInfo";
  },
  [PrequalificationTemplate[], number]
>;

export const calculatePQWavPassingLevel = (currency: TypeOf<typeof Currency>, OEValue: number): string => {
  // 
  if (currency === "IDR") {
    // 
    if (OEValue >= 5_000_000_000 && OEValue <= 200_000_000_000) {
      return ">=50%"
    }
    else if (OEValue >= 200_000_000_000 && OEValue <= 1_000_000_000_000) {
      return ">=55%";
    }
    else if (OEValue >= 1_000_000_000_000) {
      return ">=60%"
    }
  }

  // USD
  if (OEValue >= 5_000_000 && OEValue <= 20_000_000) {
    return ">=50%"
  }
  else if (OEValue >= 20_000_000 && OEValue <= 100_000_000) {
    return ">=55%";
  }
  else if (OEValue >= 100_000_000) {
    return ">=60%"
  }

  return "0%";
}

