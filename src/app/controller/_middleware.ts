import express from "express";
import { CustomRequest, RequestWithContext } from "../../framework/controller_express.js";
import jwt, { JwtPayload } from "jsonwebtoken";

export const handleJWTAuth = (secretKey: string) => async (req: RequestWithContext, res: express.Response, next: express.NextFunction) => {
  // 
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      res.sendStatus(401);
      return;
    }

    const token = authHeader.split(" ");
    if (token.length !== 2 || token[0].toLowerCase() !== "bearer") {
      res.sendStatus(401);
      return;
    }

    const dataDecoded = jwt.verify(token[1], secretKey) as JwtPayload;

    if (req.ctx) {
      req.ctx.data["userLogin"] = {
        ...dataDecoded.data,
        isAdmin: dataDecoded.data.isAdmin ?? null,
        isAdminProcPlan: dataDecoded.data.isAdminProcPlan ?? null,
        isAdminRequisition: dataDecoded.data.isAdminRequisition ?? null,
      }; // split between user and vendor login data
    }

    next();
  } catch (e: any) {
    next(e);
  }
};

export const handleError = () => (err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  //

  const errorTypes = [jwt.JsonWebTokenError, jwt.NotBeforeError, jwt.TokenExpiredError];

  if (errorTypes.some((errorType) => err instanceof errorType)) {
    res.sendStatus(401);
    return;
  }

  return res.status(400).json({
    status: 400,
    message: err.message,
  });

  //
};
