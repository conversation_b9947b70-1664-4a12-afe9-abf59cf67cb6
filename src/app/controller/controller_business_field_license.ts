import { HTTPDataItem } from "../../framework/data_http.js";
import { arrayOfString, queryPageAndSize, userLogin } from "./_reused_properties.js";

export const controllerBusinessFieldLicense: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/businessFieldLicense",
    usecase: "businessFieldLicenseGetAll",
    tag: "business_field_license",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      search: { type: "string" },
      documentName: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/businessFieldLicenseDocuments",
    usecase: "businessFieldLicenseDocumentGetAll",
    tag: "business_field_license_document",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      search: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },
];
