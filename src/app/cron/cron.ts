import cron from "node-cron";
import { DataSource } from "typeorm";
import { sendGeneralApprovalReminderMail } from "../utility/cron.js";
import { importDoA } from "../utility/delegation.js";
import { getIndonesianHolidays } from "../utility/national_calendar.js";
import { importPrItem } from "../utility/pr_item.js";

export const scheduledCronJobs = async (ds: DataSource) => {
    // 
    cron.schedule("* * * * *", async () => {
        // 
        // console.log(">> RUNNING CRON JOB (1 MINUTE)");
        // await sendGeneralApprovalReminderMail(ds);
        // console.log(">> CRON JOB DONE (1 MINUTE)");
    });

    cron.schedule("*/5 * * * * *", async () => {
        // console.log(">> RUNNING CRON JOB (5 SECONDS)");
        // await sendGeneralApprovalReminderMail(ds);
        // console.log(">> CRON JOB DONE (5 SECONDS)");
    });

    cron.schedule("0 0 * * *", async () => {
        // 
        // console.log(">> RUNNING CRON JOB (MIDNIGHT)");
        // console.log(">> CRON JOB DONE (MIDNIGHT)");
    });

    cron.schedule("30 0 * * *", async () => {
        // console.log(">> RUNNING CRON JOB (12:30 AM)");
        // console.log(">> CRON JOB DONE (12:30 AM)");
    });

    cron.schedule("0 7 * * *", async () => {
        console.log(">> RUNNING CRON JOB (7 AM)");
        await sendGeneralApprovalReminderMail(ds);
        console.log(">> CRON JOB DONE (7 AM)");
    });
    
    cron.schedule("15 7 * * *", async () => {
        console.log(">> RUNNING CRON JOB (7 AM)");
        await importPrItem(ds);
        await importDoA(ds);
        console.log(">> CRON JOB DONE (7 AM)");
    });

    cron.schedule("0 12 * * *", async () => {
        // console.log(">> RUNNING CRON JOB (12 PM)");
        // console.log(">> CRON JOB DONE (12 PM)");
    });

    cron.schedule("15 12 * * *", async () => {
        console.log(">> RUNNING CRON JOB (12:15 PM)");
        await importDoA(ds);
        console.log(">> CRON JOB DONE (12:15 PM)");
    });

    cron.schedule("0 0 1 1 *", async () => {
        console.log(">> RUNNING CRON JOB (NEW YEAR FIRST DAY)");
        const year = new Date().getFullYear();
        await getIndonesianHolidays(ds, year);
        console.log(">> CRON JOB DONE (NEW YEAR FIRST DAY)");
    });
}